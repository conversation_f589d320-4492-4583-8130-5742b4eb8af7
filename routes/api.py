from flask import request, jsonify, session, flash, redirect, url_for, current_app, render_template # Added current_app and render_template
from datetime import datetime, timedelta
import google.generativeai as genai
from pinecone import Pinecone
import os
import re
import json
import base64
import urllib.parse
import time
import markdown
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
from models import db, Part, Submission, IncompleteSubmission, ProblemSetSubmission, MarkingPoint, DailyActivity, DailyActiveTime, User, Question, NotesChunk, NotesEmbedding
# Import utilities and decorators
from .utils import login_required, update_user_activity, error_logger, app_logger

# Note: AI clients (Groq, Mistral) might be better initialized in the app factory
# and passed here, or accessed via app.config/current_app.
# For now, assume they are passed to the registration function.

class GradingTimer:
    """Utility class to track timing for grading steps"""
    def __init__(self):
        self.steps = []
        self.start_time = time.time()
        self.current_step_start = None

    def start_step(self, step_name):
        """Start timing a new step"""
        current_time = time.time()
        if self.current_step_start is not None:
            # End the previous step
            self.end_current_step()
        self.current_step_start = current_time
        self.current_step_name = step_name

    def end_current_step(self):
        """End the current step and record its duration"""
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None

    def get_summary(self):
        """Get a summary of all timing steps"""
        # End current step if still running
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

def _get_relevant_notes_sections(question_text: str, max_sections: int = 3):
    """
    Get relevant notes sections for a question using the RAG system.

    Args:
        question_text: The question text to search for
        max_sections: Maximum number of sections to return (default: 3)

    Returns:
        List of relevant notes sections with metadata
    """
    try:
        # Check if RAG dependencies are available
        try:
            from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
            if not DEPENDENCIES_AVAILABLE:
                return []
        except ImportError:
            return []

        # Initialize RAG system
        rag_system = NotesRAGSystem()

        # Search for relevant chunks
        results = rag_system.search_similar_chunks(
            question_text,
            top_k=max_sections,
            min_score=0.4  # Higher threshold for question relevance
        )

        # Format results for frontend
        relevant_sections = []
        for result in results:
            chunk = result['chunk']
            section = {
                'title': chunk['title'],
                'content': chunk['content'][:300] + '...' if len(chunk['content']) > 300 else chunk['content'],
                'filename': chunk['filename'],
                'chapter_id': chunk['chapter_id'],
                'section_id': chunk['section_id'],
                'similarity_score': result['similarity_score'],
                'relevance_type': result['relevance_type'],
                'url': f"/notes/{chunk['chapter_id']}#{chunk['section_id']}"
            }
            relevant_sections.append(section)

        return relevant_sections

    except Exception as e:
        # Log error but don't fail the grading process
        print(f"Warning: Error getting relevant notes sections: {e}")
        return []

def register_api_routes(app, db, session, limiter, groq_client, mistral_client, gemini_grading_client): # Pass limiter and AI clients
    def _process_single_marking_point(mp_data, mp_index, user_answer, part_data, marking_points_data,
                                     app_logger, assigned_border_class):
        """
        Process a single marking point and return the evaluation result.
        This function is designed to be called in parallel for each marking point.
        """
        point_score = 0
        is_correct_mp = False
        is_partial_mp = False
        evidence_mp = None
        evidence_indices = []  # List of [start, end] character indices
        error_mp = False

        try:

            # Generate LLM prompt
            prompt = f"""\
                You are an expert examiner evaluating a student's answer against a specific marking point.
                TASK:
                1. Determine if the student's answer demonstrates understanding of the marking point
                2. Classify the answer and provide structured feedback in one of these formats:
                   - If FULLY CORRECT: "Correctly identified <concept>"
                   - If the student attempted to address the concept but was INCORRECT: "Incorrecty explained <concept"
                   - If the student OMITTED the concept entirely: "Omitted <concept>"
                3. For FULLY or PARTIALLY correct answers, identify the exact text from the student's answer that provides evidence
                4. I am using the same prompt for all marking points, and so if you think that a part of the answer is MORE relevant to the other point instead of this marking point, you MUST not include it in the EVIDENCE section because it will be counted as evidence for the other marking point. In other words, the same text CANNOT appear in the EVIDENCE section for multiple marking points.

                RESPONSE FORMAT:
                You must respond in one of these four formats ONLY:

                Format 1 - If the marking point is FULLY addressed:
                YES
                EVIDENCE: <exact text from student's answer>

                Format 2 - If the marking point was PARTIALLY addressed
                PARTIAL
                EVIDENCE: <exact text from student's answer>

                Format 3 - If the marking point is NOT addressed
                NO

                IMPORTANT: Do not include any other text, explanations, or formatting in your response.

                MARKING POINT: {mp_data['description']}
                OTHER MARKING POINTS (exclude these from your evaluation): {', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
                STUDENT'S ANSWER: {user_answer}
                """

            # Call LLM
            generation_config = {
                "temperature": 0.1, "top_p": 0.95, "top_k": 40, "max_output_tokens": 4096,
            }
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
            response_text = gemini_grading_client.generate_content(
                prompt,
                generation_config=generation_config,
                safety_settings=safety_settings
            ).text.strip()

            # Process response
            response_upper = response_text.upper()
            is_correct_mp = 'YES' in response_upper and not 'PARTIAL' in response_upper
            is_partial_mp = 'PARTIAL' in response_upper

            app_logger.debug(f"LLM Response for marking point {mp_data['id']}: {response_text}")

            # Extract evidence for correct/partial answers and find character indices
            if is_correct_mp or is_partial_mp:
                if 'EVIDENCE:' in response_text:
                    evidence_parts = response_text.split('EVIDENCE:', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].strip()
                elif 'EVIDENCE' in response_text: # Fallback if colon is missing
                    evidence_parts = response_text.split('EVIDENCE', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].lstrip(':').strip()

                # Find character indices for the evidence text in the user answer
                if evidence_mp:
                    import re
                    # Clean up evidence text (remove extra whitespace and normalize)
                    evidence_clean = re.sub(r'\s+', ' ', evidence_mp.strip())

                    # Try exact match first
                    evidence_lower = evidence_clean.lower()
                    user_answer_lower = user_answer.lower()
                    start_pos = 0

                    while True:
                        pos = user_answer_lower.find(evidence_lower, start_pos)
                        if pos == -1:
                            break
                        evidence_indices.append([pos, pos + len(evidence_clean)])
                        start_pos = pos + 1

                    # If no exact match found, try fuzzy matching for key terms
                    if not evidence_indices:
                        # Split evidence into key terms and look for them individually
                        key_terms = [term.strip() for term in evidence_clean.split() if len(term.strip()) > 2]

                        for term in key_terms:
                            term_lower = term.lower()
                            start_pos = 0
                            while True:
                                pos = user_answer_lower.find(term_lower, start_pos)
                                if pos == -1:
                                    break
                                evidence_indices.append([pos, pos + len(term)])
                                start_pos = pos + 1

            # Calculate score
            if is_correct_mp:
                point_score = mp_data['score']
            elif is_partial_mp:
                point_score = mp_data['score'] * 0.5

        except Exception as e_mp:
            app_logger.exception(f"Error evaluating marking point {mp_data['id']} with LLM: {str(e_mp)}")
            error_mp = True

        # Return result
        return {
            'id': mp_data['id'],
            'description': mp_data['description'],
            'score': mp_data['score'],
            'achieved': is_correct_mp,
            'partial': is_partial_mp,
            'achieved_score': point_score,
            'evidence': evidence_mp,
            'evidence_indices': evidence_indices,  # New: character indices for highlighting
            'feedback': mp_data['description'],  # Return the marking point description as the correct answer
            'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_mp else None,
            'error': error_mp,
            'mp_index': mp_index  # Include index for ordering results
        }

    # --- IMPORTANT: GRADING FUNCTION! ---
    def _calculate_score_and_evaluated_points(user_answer: str, part_data: Part, gemini_model, app_logger):
        """
        Calculates the score for a given part and returns evaluated marking points.
        This logic is shared between get_git_diff and submit_problemset.
        """
        # Initialize timing tracker
        timer = GradingTimer()
        timer.start_step("Initialization and Setup")

        total_score = 0
        evaluated_points = []

        if part_data.input_type == 'mcq':
            timer.start_step("MCQ Processing")
            # For MCQ, the answer is the selected option index (passed as user_answer)
            if not user_answer: # Should be validated before calling this helper
                timer.start_step("MCQ Error - No Answer")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'error': 'No answer provided for MCQ', 'timing': timing_summary}

            try:
                timer.start_step("MCQ Validation")
                selected_option_index = int(user_answer)
                options = part_data.options

                if not options or selected_option_index >= len(options) or selected_option_index < 0:
                    timer.start_step("MCQ Error - Invalid Option")
                    timing_summary = timer.get_summary()
                    return {'score': 0, 'evaluated_points': [], 'error': 'Invalid option selected for MCQ', 'timing': timing_summary}

                timer.start_step("MCQ Scoring")
                selected_option = options[selected_option_index]
                is_correct = selected_option.is_correct # Assuming is_correct is a boolean field
                total_score = part_data.score if is_correct else 0

                timer.start_step("MCQ Feedback Generation")
                # For MCQs, create structured feedback based on correctness
                if is_correct:
                    feedback_text = f"Correctly identified {selected_option.description}"
                else:
                    # Find the correct option for feedback
                    correct_option = next((opt for opt in options if opt.is_correct), None)
                    if correct_option:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                    else:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

                timer.start_step("MCQ Result Compilation")
                evaluated_points.append({
                    'id': f"mcq_{part_data.id}",
                    'description': f"Selected option: {selected_option.description}",
                    'score': part_data.score,
                    'achieved': is_correct,
                    'partial': False,
                    'achieved_score': total_score,
                    'evidence': str(selected_option_index),
                    'feedback': feedback_text,
                    'color': 'border-green-400' if is_correct else 'border-red-400'
                })

                timing_summary = timer.get_summary()
                return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

            except (ValueError, TypeError) as e:
                timer.start_step("MCQ Error - Exception")
                app_logger.exception(f"Error processing MCQ answer in helper: {str(e)}")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'error': 'Invalid MCQ answer format', 'timing': timing_summary}

        # For non-MCQ questions (free response with marking points)
        timer.start_step("Free Response Setup")
        try:
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part_data.marking_points]

            highlight_border_classes = [
                'border-yellow-400', 'border-blue-400', 'border-green-400',
                'border-pink-400', 'border-purple-400', 'border-indigo-400',
                'border-teal-400', 'border-orange-400', 'border-lime-400',
                'border-cyan-400'
            ]
            color_index = 0

            timer.start_step(f"Processing {len(marking_points_data)} Marking Points in Parallel")
            parallel_start_time = time.time()

            # Process all marking points in parallel
            with ThreadPoolExecutor(max_workers=min(len(marking_points_data), 10)) as executor:
                # Prepare tasks for parallel execution
                future_to_mp = {}
                for mp_index, mp_data in enumerate(marking_points_data):
                    assigned_border_class = highlight_border_classes[color_index % len(highlight_border_classes)]
                    color_index += 1

                    # Submit task to thread pool
                    future = executor.submit(
                        _process_single_marking_point,
                        mp_data, mp_index, user_answer, part_data, marking_points_data,
                        app_logger, assigned_border_class
                    )
                    future_to_mp[future] = mp_index

                # Collect results as they complete
                results = [None] * len(marking_points_data)  # Pre-allocate list to maintain order
                completed_count = 0
                for future in as_completed(future_to_mp):
                    try:
                        result = future.result()
                        mp_index = result['mp_index']
                        results[mp_index] = result
                        completed_count += 1
                        app_logger.debug(f"Completed marking point {mp_index + 1}/{len(marking_points_data)} in parallel")
                    except Exception as e:
                        mp_index = future_to_mp[future]
                        app_logger.exception(f"Error in parallel processing of marking point {mp_index}: {str(e)}")
                        # Create error result
                        mp_data = marking_points_data[mp_index]
                        results[mp_index] = {
                            'id': mp_data['id'],
                            'description': mp_data['description'],
                            'score': mp_data['score'],
                            'achieved': False,
                            'partial': False,
                            'achieved_score': 0,
                            'evidence': None,
                            'feedback': mp_data['description'],
                            'color': None,
                            'error': True,
                            'mp_index': mp_index
                        }
                        completed_count += 1

            parallel_end_time = time.time()
            parallel_duration = parallel_end_time - parallel_start_time

            # Add parallel processing timing information
            timer.steps.append({
                'name': f"Parallel Processing of {len(marking_points_data)} Marking Points",
                'duration_ms': round(parallel_duration * 1000, 2),
                'duration_s': round(parallel_duration, 3),
                'details': f"Processed {len(marking_points_data)} marking points concurrently"
            })

            timer.start_step("Result Compilation and Score Calculation")
            # Calculate total score and prepare evaluated points
            total_score = 0
            evaluated_points = []
            for result in results:
                if result:  # Ensure result is not None
                    total_score += result['achieved_score']
                    # Remove mp_index from result before adding to evaluated_points
                    result_copy = result.copy()
                    result_copy.pop('mp_index', None)
                    evaluated_points.append(result_copy)

            timer.start_step("Final Score Compilation")
            timing_summary = timer.get_summary()
            return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

        except Exception as e:
            timer.start_step("Error Handling - Free Response")
            app_logger.exception(f"Error processing free-response answer in helper: {str(e)}")
            timing_summary = timer.get_summary()
            # Return 0 score and empty points if a major error occurs in this block
            return {'score': 0, 'evaluated_points': [], 'error': 'Error processing marking points', 'timing': timing_summary}
    # --- END: Helper function for grading ---

    # --- START: New Supabase Auth Confirmation Route ---
    @app.route('/api/auth/confirm', methods=['GET'])
    def confirm_auth():
        """
        Handles email confirmation links (e.g., password reset, email change) using Supabase PKCE flow.
        Verifies the token_hash and type, or exchanges code for session, and redirects.
        """
        # Check for code parameter (new PKCE flow)
        code = request.args.get('code')
        if code:
            app_logger.info(f"Auth confirmation request received with code parameter")
            next_url = request.args.get('next', url_for('login'))  # Default redirect to login

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                # Exchange the code for a session
                app_logger.info(f"Attempting to exchange code for session...")
                exchange_response = supabase.auth.exchange_code_for_session({
                    "auth_code": code
                })
                app_logger.info(f"Code exchange response: {exchange_response}")

                # Check if exchange was successful
                if exchange_response and exchange_response.user:
                    supabase_email = exchange_response.user.email
                    app_logger.info(f"Successfully exchanged code for session. User email: {supabase_email}")

                    # Store the email in session
                    session['supabase_email'] = supabase_email
                    session['auth_timestamp'] = datetime.now().timestamp()
                    session.permanent = True
                    session.modified = True

                    # Validate the next_url to prevent open redirect vulnerabilities
                    if next_url and next_url.startswith('/'):
                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(next_url)
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided: {next_url}. Redirecting to login.")
                        flash('Verification successful! Please log in.', 'success')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Code exchange failed. Response: {exchange_response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                error_logger.exception(f"Error during code exchange: {str(e)}")
                flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # Legacy flow with token_hash
        token_hash = request.args.get('token_hash')
        auth_type = request.args.get('type')
        next_url = request.args.get('next', url_for('login')) # Default redirect to login

        if token_hash and auth_type:
            app_logger.info(f"Auth confirmation request received. Type: {auth_type}, Token Hash: {token_hash[:5]}..., Next: {next_url}")

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                app_logger.info(f"Attempting Supabase verify_otp with type: {auth_type}, token_hash: {token_hash[:5]}...")
                # Verify the OTP (which includes password reset tokens)
                # Get the email from the query parameters if available
                email = request.args.get('email')

                # Use the correct schema for verifying OTP
                verify_data = {
                    "token_hash": token_hash,
                    "type": auth_type
                }

                # Add email to the verification data if available
                if email:
                    verify_data["email"] = email
                    app_logger.info(f"Including email in OTP verification: {email}")
                else:
                    app_logger.info(f"Email not found in query parameters, proceeding without it")

                response = supabase.auth.verify_otp(verify_data)
                app_logger.info(f"Supabase verify_otp response: {response}")

                # Check if verification was successful (user data should be present)
                if response and response.user:
                    supabase_email = response.user.email
                    app_logger.info(f"Supabase user verified: {supabase_email}")

                    # Validate the next_url to prevent open redirect vulnerabilities
                    # Allow only relative paths starting with '/'
                    if next_url and next_url.startswith('/'):
                        # Store the Supabase email and token_hash in the session for use in the reset_password route
                        session['supabase_email'] = supabase_email
                        session['token_hash'] = token_hash
                        session['auth_timestamp'] = datetime.now().timestamp()  # Add timestamp for session freshness check
                        session.permanent = True  # Make session persistent
                        session.modified = True  # Mark session as modified to ensure it's saved

                        # Add the email and token to the redirect URL as query parameters as a fallback
                        # URL encode the parameters to ensure they're properly formatted
                        encoded_email = urllib.parse.quote(supabase_email)
                        encoded_token = urllib.parse.quote(token_hash)
                        redirect_url = f"{next_url}?email={encoded_email}&token_hash={encoded_token}"
                        app_logger.info(f"Adding email and token to redirect URL as fallback: {redirect_url}")

                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(redirect_url) # Redirect to the URL with query parameters as fallback
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided in confirmation link: {next_url}. Redirecting verified user {supabase_email} to login.")
                        flash('Verification link error. Redirecting to login.', 'warning')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Supabase verify_otp failed for type {auth_type}. Response: {response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                # Catch potential exceptions from Supabase client or other issues
                error_logger.exception(f"Error during auth confirmation ({auth_type}): {str(e)}")
                # Check for specific Supabase errors if possible (e.g., invalid token)
                if "invalid token" in str(e).lower():
                    flash('Verification failed: Invalid or expired link.', 'error')
                else:
                    flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # If we get here, neither code nor token_hash+type were provided
        error_logger.warning("Auth confirmation missing required parameters (code or token_hash+type).")
        flash('Invalid or incomplete confirmation link.', 'error')
        return redirect(url_for('login'))
    # --- END: New Supabase Auth Confirmation Route ---


    # --- Other API Endpoints ---

    @app.route("/check_answer/<int:question_id>/<int:part_id>", methods=['POST'])
    @limiter.limit("30/minute") # Apply rate limit
    @login_required # Require login to check answers
    def check_answer(question_id, part_id):
        if 'user_id' not in session:
            error_logger.warning("Unauthorized attempt to submit answer")
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image inputs
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')
        confidence_level = request.form.get('confidence_level', 'Medium')

        if not user_answer and not image_file:
            error_logger.info(f"Empty answer submitted - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer or upload an image'
            }), 400

        try:
            # Process image if provided
            if image_file:
                # Validate file type
                if not image_file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid file type. Please upload a PNG, JPG, or JPEG image.'
                    }), 400

                # Encode the image to base64
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro')

                # Create multimodal prompt
                prompt_parts = [
                    "Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
                app_logger.info(f"Image received for OCR - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")

            # Use the helper function to calculate score and get evaluated points
            grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
            current_score = grading_details['score']
            feedback_to_user = "Your answer has been submitted and graded."

            # Store evaluation details as JSON in feedback field
            import json
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': current_score,
                'max_score': part_data.score
            }
            feedback_json = json.dumps(feedback_data)

            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,
                score=current_score,
                feedback=feedback_json
            )
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            app_logger.info(
                f"Answer submitted - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, "
                f"Score: {submission.score}/{part_data.score}"
            )

            return jsonify({
                'status': 'success',
                'feedback': feedback_to_user,
                'score': submission.score,
                'submission_id': submission.id,
                'max_score': part_data.score
            })

        except Exception as e:
            db.session.rollback()
            error_logger.exception(
                f"Error processing submission - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500



    @app.route('/get_git_diff/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def get_git_diff(question_id, part_id):
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)
        question_data = Question.query.get_or_404(question_id)

        # Handle both text and image submissions
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')

        # If an image was submitted, process it
        if image_file and image_file.filename:
            try:
                # Convert image to base64 for API processing
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

                # Create multimodal prompt
                prompt_parts = [
                    "You are a LaTeX expert. Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
            except Exception as e:
                error_logger.exception(f"Error processing image submission: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Error processing image submission'
                }), 500

        if not user_answer:
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer'
            }), 400

        # Use the new helper function to calculate score and get evaluated points
        grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
        print(grading_details)
        total_score = grading_details['score']
        evaluated_points = grading_details['evaluated_points']

        if 'error' in grading_details and part_data.input_type == 'mcq': # Handle MCQ specific errors from helper
            return jsonify({'status': 'error', 'message': grading_details['error']}), 400

        # If it's an MCQ, the helper already determined the score and basic feedback.
        # We can return a simplified response for MCQs directly after calling the helper.
        if part_data.input_type == 'mcq':
            # Store evaluation details as JSON in feedback field
            import json
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': total_score,
                'max_score': part_data.score
            }
            feedback_json = json.dumps(feedback_data)

            # Create submission record
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,  # user_answer is the selected_option_index for MCQ
                score=total_score,
                feedback=feedback_json
            )
            db.session.add(submission)
            db.session.commit()

            is_correct_mcq = total_score > 0
            return jsonify({
                'status': 'success',
                'score': total_score,
                'max_score': part_data.score,
                'is_correct': is_correct_mcq,
                'feedback': 'Your answer is correct.' if is_correct_mcq else 'Your answer is incorrect.',
                'marking_points': evaluated_points, # Contains the single MCQ point
                'timing': grading_details.get('timing', {})  # Include timing information
            })

        # For non-MCQ, proceed with database operations and detailed response
        try:
            import json

            # Extract context-dependent data
            current_user_id = session['user_id']

            # Prepare feedback data for storage
            feedback_data = {
                'evaluated_points': grading_details.get('evaluated_points', []),
                'timing': grading_details.get('timing', {}),
                'total_score': total_score,
                'max_score': part_data.score
            }

            # Store evaluation details as JSON in feedback field
            feedback_json = json.dumps(feedback_data)

            # Create a new submission record in the database
            submission = Submission(
                user_id=current_user_id,
                question_id=question_id,
                part_id=part_id,
                answer=user_answer, # Store original answer
                score=total_score,
                feedback=feedback_json
            )

            # Add and commit to the database
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(current_user_id)
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {current_user_id}: {e}")

            app_logger.info(f"Database operations completed for user {current_user_id}")

            # Create highlight_data for the highlighted_answer function
            import html
            highlight_data = []
            for point in evaluated_points:
                if (point.get('achieved') or point.get('partial')) and point.get('evidence') and point.get('color'):
                    # Use a dashed border for partial answers, solid for full credit
                    border_style = "border-b-2 " if point.get('achieved') else "border-b-2 border-dashed "
                    highlight_data.append({
                        'evidence': point['evidence'],
                        'color_class': border_style + point['color'], # e.g., 'border-yellow-400'
                        'feedback': point.get('feedback', ''), # Return the correct answer as feedback
                        'description': point.get('description', ''), # Include marking point description
                        'achieved': point.get('achieved', False),
                        'partial': point.get('partial', False),
                        'score': point.get('achieved_score', 0)
                    })

            return jsonify({
                'status': 'success',
                'marking_points': evaluated_points,
                'score': total_score,
                'max_score': part_data.score,
                'timing': grading_details.get('timing', {}),  # Include timing information
                'highlight_data': highlight_data  # Include highlight_data for frontend
            })

        except Exception as e:
            error_logger.exception(
                f"Error processing answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500


    @app.route('/highlighted_answer/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def highlighted_answer(question_id, part_id):
        """Returns the highlighted answer for a given question and part."""
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get highlighted answer'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Get highlight_data and user_answer from request
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'status': 'error',
                    'message': 'No data provided'
                }), 400

            highlight_data = data.get('highlight_data', [])
            user_answer = data.get('user_answer', '')

            if not user_answer:
                return jsonify({
                    'status': 'error',
                    'message': 'No user answer provided'
                }), 400

        except Exception as e:
            error_logger.exception(f"Error parsing request data: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'Invalid request data'
            }), 400

        # For MCQ, return simple response
        if part_data.input_type == 'mcq':
            return jsonify({
                'status': 'success',
                'answer': user_answer  # For MCQ, just return the plain answer
            })

        # For non-MCQ, create highlighted answer using Gemini
        try:
            import html
            import json

            highlighted_answer = html.escape(user_answer) # Default to escaped original answer
            if highlight_data:
                # Create the prompt for highlighting with feedback information
                highlight_prompt = r"""\
                You are an HTML expert. Given a piece of text and a list of evidence snippets with associated Tailwind CSS border classes and feedback, modify the original text by wrapping EACH evidence snippet EXACTLY as it appears in the text with a span tag that includes both highlighting and tooltip feedback.

                **Instructions:**
                1. Find the occurrences of the evidence snippets within the original text. Do NOT modify the user answer (including changing the order of the answer), just highlight the relevant parts.
                2. Wrap each found evidence snippet with a span tag that includes:
                   - The color class for visual highlighting
                   - A title attribute containing the structured feedback for tooltips
                   - Format: <span class="{{color_class}}" title="{{feedback}}">evidence text</span>
                3. If points overlap, you need to deconflict them in the way that makes the most sense.
                4. Output ONLY the final, complete HTML string. Do not include any introductory text, explanations, or markdown formatting.
                5. Ensure ALL highlighted data is included and inside the appropriately colored spans with feedback tooltips.
                6. Do NOT write stuff like Note: xxx at the end of your output if there are ambiguities.
                7. The order of the marking points in the user answer may be jumbled up. However, you should still give credit and underline parts even if they aren't in order.
                8. Make sure the title attribute is properly escaped for HTML (replace quotes with &quot; if needed).
                9. Write your final result as: FINAL RESULT: <your_html_output>
                """
                other=f"""**User answer:**
                {user_answer}

                **Highlight Data (Evidence, CSS Class, and Feedback):**
                {json.dumps(highlight_data, indent=2)}

                **Example format for each highlighted section: (Note there may be more than one, you must include all)**
                <span class="border-b-2 border-yellow-400" title="Correctly identified kinetic energy formula">KE = 1/2mv²</span>

                **Final HTML Output:**
                """
                highlight_prompt = highlight_prompt + other
                try:
                    # Use Gemini 2.5 Flash for highlighting with strict parameters
                    gemini_pro_model = genai.GenerativeModel('gemini-2.5-flash')

                    generation_config = {
                        "temperature": 0.1,  # Use deterministic output
                        "top_p": 0.95,
                        "top_k": 40,
                    }

                    response = gemini_pro_model.generate_content(
                        highlight_prompt,
                        generation_config=generation_config,
                    )

                    # Check if response was blocked by safety filters
                    if response.candidates and response.candidates[0].finish_reason == 2:  # SAFETY
                        app_logger.warning("Gemini highlighting response blocked by safety filters")
                        highlighted_answer = html.escape(user_answer)
                    elif not response.text:
                        app_logger.warning("Gemini highlighting response has no text content")
                        highlighted_answer = html.escape(user_answer)
                    else:
                        highlighted_answer_raw = response.text.strip()
                        app_logger.info(f"Raw Gemini highlighting response: {highlighted_answer_raw}")

                        if 'FINAL RESULT: ' in highlighted_answer_raw:
                            highlighted_answer_raw = highlighted_answer_raw.split('FINAL RESULT: ')[1]

                            # Basic validation (optional but recommended)
                            if '<span' in highlighted_answer_raw and '</span>' in highlighted_answer_raw:
                                highlighted_answer = highlighted_answer_raw
                            else:
                                # Log a warning if the response doesn't look like HTML
                                app_logger.warning(f"Gemini highlighting response did not seem to contain valid spans: {highlighted_answer_raw}")
                                # Fallback to the default escaped answer
                                highlighted_answer = html.escape(user_answer)
                        else:
                            app_logger.warning("Gemini response missing 'FINAL RESULT: ' marker")
                            highlighted_answer = html.escape(user_answer)

                except Exception as e:
                    error_logger.exception(f"Error calling Gemini API for highlighting: {str(e)}")
                    # Fallback to the default escaped answer in case of API error
                    highlighted_answer = html.escape(user_answer)

            # Use the highlighted answer
            highlighted_answer_html = highlighted_answer

            return jsonify({
                'status': 'success',
                'answer': highlighted_answer_html
            })

        except Exception as e:
            error_logger.exception(
                f"Error creating highlighted answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while creating highlighted answer'
            }), 500


    @app.route('/auto_save', methods=['POST'])
    @login_required
    def auto_save():
        """Saves incomplete progress for a problem set part."""
        user_id = session['user_id']
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        problemset_id = data.get('problemset_id')
        question_id = data.get('question_id')
        part_id = data.get('part_id')
        answer = data.get('answer') # Can be None or empty

        if not all([problemset_id, question_id, part_id]):
            return jsonify({'status': 'error', 'message': 'Missing required data (problemset, question, part IDs).'}), 400

        try:
            # Get or create incomplete submission record with retry logic built into the model method
            submission = IncompleteSubmission.get_or_create(
                problemset_id=problemset_id,
                user_id=user_id,
                question_id=question_id,
                part_id=part_id
            )

            # Update answer and timestamp
            from sqlalchemy.exc import OperationalError
            import time

            max_retries = 3
            retry_delay = 0.1  # seconds

            for attempt in range(max_retries):
                try:
                    submission.answer = answer # Allow None or empty string
                    submission.last_updated = datetime.now()
                    db.session.commit()
                    # app_logger.debug(f"Autosaved progress for User: {user_id}, PS: {problemset_id}, Q: {question_id}, P: {part_id}")
                    return jsonify({'status': 'success'})

                except OperationalError as e:
                    # Handle database locks specifically
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        db.session.rollback()
                        time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        db.session.rollback()
                        raise

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error during auto_save for User {user_id}, PS {problemset_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to save progress.'}), 500


    @app.route('/get_saved_progress/<int:problemset_id>')
    @login_required
    def get_saved_progress(problemset_id):
        """Retrieves saved incomplete progress for a problem set."""
        user_id = session['user_id']
        try:
            submissions = IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=user_id
            ).all()

            # Format data for the frontend
            progress_data = [{
                'question_id': sub.question_id,
                'part_id': sub.part_id,
                'answer': sub.answer if sub.answer is not None else '' # Ensure frontend gets string
            } for sub in submissions]

            return jsonify({
                'status': 'success',
                'submissions': progress_data
            })
        except Exception as e:
            error_logger.exception(f"Error fetching saved progress for User {user_id}, PS {problemset_id}: {e}")
            # Return 200 OK but with error status for AJAX handling
            return jsonify({'status': 'error', 'message': 'Could not retrieve saved progress.'}), 200


    @app.route('/submit_problemset', methods=['POST'])
    @limiter.limit("10/minute") # Limit submission frequency
    @login_required
    def submit_problemset():
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        data = request.get_json()
        problemset_id = data.get('problemset_id')
        submissions = data.get('submissions', [])  # List of {question_id, part_id, answer}

        if not problemset_id:
            return jsonify({'status': 'error', 'message': 'Missing problemset_id'}), 400

        # Initialize the Gemini model once for all parts in this problemset submission

        try:
            # Create a new problemset submission
            problemset_submission = ProblemSetSubmission(
                problemset_id=problemset_id,
                user_id=session['user_id'],
                status='completed',
                submitted_at=datetime.now()
            )
            db.session.add(problemset_submission)
            db.session.flush()  # Get the ID of the new submission

            # Process each answer
            submission_results = []
            for submission_data in submissions:
                question_id = submission_data.get('question_id')
                part_id = submission_data.get('part_id')
                answer = submission_data.get('answer', '').strip()

                if not all([question_id, part_id]):
                    continue

                # Get the part to check its answer and score
                part = Part.query.get(part_id)
                if not part:
                    continue

                # Use the helper function to calculate the score for this part
                # Note: submit_problemset doesn't have Pinecone index, so pass None
                grading_details = _calculate_score_and_evaluated_points(answer, part, gemini_grading_client, app_logger)
                current_score = grading_details['score']

                # Store evaluation details as JSON in feedback field
                import json
                feedback_data = {
                    'evaluated_points': grading_details.get('evaluated_points', []),
                    'timing': grading_details.get('timing', {}),
                    'total_score': current_score,
                    'max_score': part.score
                }
                feedback_json = json.dumps(feedback_data)

                # Create the submission
                submission = Submission(
                    user_id=session['user_id'],
                    question_id=question_id,
                    part_id=part_id,
                    answer=answer,
                    score=current_score,
                    feedback=feedback_json
                )
                db.session.add(submission)
                problemset_submission.question_submissions.append(submission)

                # Add submission result with feedback
                submission_results.append({
                    'question_id': question_id,
                    'part_id': part_id,
                    'score': current_score,
                    'max_score': part.score,
                    'feedback': "Answer submitted and graded."
                })

            # Calculate scores
            problemset_submission.calculate_scores()

            # Delete incomplete submissions for this problemset
            IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=session['user_id']
            ).delete()

            db.session.commit()

            # Trigger feed generation for the user after problemset submission
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            return jsonify({
                'status': 'success',
                'message': 'Problem set submitted successfully',
                'submission_id': problemset_submission.id,
                'submissions': submission_results
            })

        except Exception as e:
            db.session.rollback()
            print(f"Error submitting problem set: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500



    # --- Marking Point AJAX Endpoints ---

    @app.route('/extract_marking_points/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def extract_marking_points(part_id):
        """Extract marking points from a question part using an API"""
        part = Part.query.get_or_404(part_id)

        try:
            # Here you would call your API to extract marking points
            # This is a placeholder for the API call
            # api_response = your_api_call(part.description, part.answer)

            # For now, we'll create a sample response
            api_response = {
                'marking_points': [
                    {'description': 'Correct formula', 'score': 2.0},
                    {'description': 'Correct substitution', 'score': 1.0},
                    {'description': 'Correct final answer', 'score': 1.0}
                ]
            }

            # Clear existing auto-generated marking points
            MarkingPoint.query.filter_by(
                part_id=part_id,
                is_auto_generated=True
            ).delete()

            # Add new marking points
            for i, point in enumerate(api_response['marking_points']):
                marking_point = MarkingPoint(
                    part_id=part_id,
                    description=point['description'],
                    score=point['score'],
                    order=i,
                    is_auto_generated=True
                )
                db.session.add(marking_point)

            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking points extracted successfully',
                'marking_points': [{
                    'id': mp.id,
                    'description': mp.description,
                    'score': mp.score,
                    'order': mp.order
                } for mp in part.marking_points]
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500


    @app.route('/update_marking_point/<int:marking_point_id>', methods=['PUT'])
    @login_required # Should be admin_required?
    def update_marking_point(marking_point_id):
        """Updates a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        try:
            updated = False
            if 'description' in data:
                new_desc = data['description'].strip()
                if new_desc: # Don't allow empty description
                     marking_point.description = new_desc
                     updated = True
                else:
                     return jsonify({'status': 'error', 'message': 'Description cannot be empty.'}), 400
            if 'score' in data:
                try:
                    new_score = float(data['score'])
                    if new_score < 0: # Allow 0 score? Let's prevent negative for now.
                         return jsonify({'status': 'error', 'message': 'Score cannot be negative.'}), 400
                    marking_point.score = new_score
                    updated = True
                except (ValueError, TypeError):
                     return jsonify({'status': 'error', 'message': 'Invalid score format.'}), 400
            if 'order' in data:
                 try:
                     marking_point.order = int(data['order'])
                     updated = True
                 except (ValueError, TypeError):
                      return jsonify({'status': 'error', 'message': 'Invalid order format.'}), 400

            if updated:
                # marking_point.validate() # Call validation if defined in model
                marking_point.is_auto_generated = False # Manual edit overrides auto-gen flag
                db.session.commit()
                app_logger.info(f"Updated marking point {marking_point_id}")
                return jsonify({
                    'status': 'success',
                    'message': 'Marking point updated.',
                    'marking_point': { # Return updated data
                        'id': marking_point.id,
                        'description': marking_point.description,
                        'score': marking_point.score,
                        'order': marking_point.order,
                        'is_auto_generated': marking_point.is_auto_generated
                    }
                })
            else:
                 return jsonify({'status': 'info', 'message': 'No changes detected.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error updating marking point.'}), 500


    @app.route('/delete_marking_point/<int:marking_point_id>', methods=['DELETE'])
    @login_required # Should be admin_required?
    def delete_marking_point(marking_point_id):
        """Deletes a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id # Get part ID for logging

        try:
            db.session.delete(marking_point)
            db.session.commit()
            app_logger.info(f"Deleted marking point {marking_point_id} from Part {part_id}")
            return jsonify({'status': 'success', 'message': 'Marking point deleted.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error deleting marking point.'}), 500


    @app.route('/add_marking_point/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def add_marking_point(part_id):
        try:
            data = request.get_json()
            part = Part.query.get_or_404(part_id)

            # Create new marking point
            marking_point = MarkingPoint(
                part_id=part_id,
                description=data.get('description', 'New marking point'),
                score=float(data.get('score', 1.0)),
                order=int(data.get('order', 0)),
                is_auto_generated=False
            )

            # Validate the marking point
            marking_point.validate()

            # Add to database
            db.session.add(marking_point)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking point added successfully',
                'marking_point': {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'order': marking_point.order
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 400



    @app.route('/move_marking_point/<int:marking_point_id>/<direction>', methods=['POST'])
    @login_required # Should be admin_required?
    def move_marking_point(marking_point_id, direction):
        """Moves a marking point up or down in order within its part."""
        # Add admin check if necessary

        if direction not in ['up', 'down']:
            return jsonify({'status': 'error', 'message': 'Invalid direction.'}), 400

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id

        try:
            # Get all marking points for this part, ordered correctly
            siblings = MarkingPoint.query.filter_by(part_id=part_id)\
                                      .order_by(MarkingPoint.order).all()

            try:
                current_index = siblings.index(marking_point)
            except ValueError:
                 # Should not happen if MP exists and belongs to the part
                 error_logger.error(f"Marking point {marking_point_id} not found in siblings list for Part {part_id}")
                 return jsonify({'status': 'error', 'message': 'Marking point consistency error.'}), 500

            if direction == 'up' and current_index > 0:
                # Swap order with the previous sibling
                prev_sibling = siblings[current_index - 1]
                marking_point.order, prev_sibling.order = prev_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} up")
                return jsonify({'status': 'success', 'message': 'Moved up.'})
            elif direction == 'down' and current_index < len(siblings) - 1:
                # Swap order with the next sibling
                next_sibling = siblings[current_index + 1]
                marking_point.order, next_sibling.order = next_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} down")
                return jsonify({'status': 'success', 'message': 'Moved down.'})
            else:
                # Cannot move further in this direction
                return jsonify({'status': 'info', 'message': 'Cannot move further.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error moving marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error moving marking point.'}), 500

    # --- Other API Endpoints ---

    @app.route("/explain_answer/<int:question_id>/<int:part_id>")
    @login_required
    @limiter.limit("10/minute")
    def explain_answer(question_id, part_id):
        """
        Explains the answer for a specific question part using Gemini LLM.
        Returns a streaming response with the explanation.
        """
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get answer explanations'
            }), 401

        update_user_activity(session['user_id'])

        try:
            # Get the part data
            part_data = Part.query.get_or_404(part_id)
            question_data = Question.query.get_or_404(question_id)

            # Initialize Pinecone for context retrieval
            pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
            index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

            # Query Pinecone for relevant context
            context_text = ""
            try:
                # Create query text from question and topic context
                query_text = f"{part_data.description}"
                if part_data.answer:
                    query_text += f" {part_data.answer}"
                if part_data.question.topic:
                    query_text += f" {part_data.question.topic.name}"
                if part_data.question.topic.subject:
                    query_text += f" {part_data.question.topic.subject.name}"

                # Query Pinecone index for relevant context
                query_payload = {
                    "inputs": {
                        "text": query_text
                    },
                    "top_k": 5  # Get more context for explanations
                }

                query_response = index.search(
                    namespace="__default__",
                    query=query_payload
                )

                for item in query_response['result']['hits']:
                    context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

            except Exception as e_pinecone:
                app_logger.warning(f"Pinecone query failed for explanation {part_id}: {str(e_pinecone)}")
                context_text = ""  # Continue without context if Pinecone fails

            # Construct the prompt
            if part_data.input_type == 'mcq':
                # For MCQ, include the options and correct answer
                options_text = ""
                correct_option = None

                for i, option in enumerate(part_data.options):
                    options_text += f"Option {i+1}: {option.description}\n"
                    if option.is_correct:
                        correct_option = i+1

                prompt = f"""
                You are an expert educational assistant. Explain the following multiple-choice question and why the correct answer is the best choice.

                QUESTION: {part_data.description}

                OPTIONS:
                {options_text}

                CORRECT ANSWER: Option {correct_option}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 150 words) of why this is the correct answer. Focus on the key concepts and principles. Use the context above to provide more detailed and accurate explanations.

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested

                # Why Option {correct_option} is Correct
                Concise explanation with clear reasoning

                """
            else:
                # For text/SAQ questions
                prompt = f"""
                You are an expert educational assistant. Explain the following question and its answer.

                QUESTION: {part_data.description}

                MODEL ANSWER: {part_data.answer}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 150 words) focusing only on the key concepts and reasoning. Use the context above to provide more detailed and accurate explanations.

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested (1-2 sentences)

                # Solution Approach
                Concise explanation of the approach to solve this problem (2-3 sentences)

                # Critical Points
                • Point 1
                • Point 2

                """

            # Create a streaming response
            def generate():
                generation_config = {
                    "temperature": 0.1,  # Slightly creative but mostly factual
                    "top_p": 0.95,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }

                safety_settings = [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                ]

                response = gemini_grading_client.generate_content(
                    prompt,
                    generation_config=generation_config,
                    safety_settings=safety_settings,
                    stream=True
                )

                for chunk in response:
                    if chunk.text:
                        yield chunk.text

            app_logger.info(f"Answer explanation requested - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return app.response_class(generate(), mimetype='text/plain')

        except Exception as e:
            error_logger.exception(f"Error generating answer explanation: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while generating the explanation'
            }), 500

    @app.route("/get_activity_data/<int:user_id>")
    @login_required # Or admin_required? Check permissions needed.
    def get_activity_data(user_id):
        """Retrieves daily activity counts for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        # e.g., is the user themselves or an admin.
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query activities for the specified user
            activities = DailyActivity.query.filter_by(user_id=user_id).all()
            # Format data as date string -> count dictionary
            activity_data = {activity.date.isoformat(): activity.activity_count for activity in activities}

            return jsonify(activity_data)
        except Exception as e:
             error_logger.exception(f"Error fetching activity data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve activity data.'}), 500

    @app.route("/get_daily_time_data/<int:user_id>")
    @login_required
    def get_daily_time_data(user_id):
        """Retrieves daily active time data for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query daily active time for the specified user
            daily_times = DailyActiveTime.query.filter_by(user_id=user_id).all()
            # Format data as date string -> seconds dictionary
            time_data = {time_entry.date.isoformat(): time_entry.active_time for time_entry in daily_times}

            return jsonify(time_data)
        except Exception as e:
             error_logger.exception(f"Error fetching daily time data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve daily time data.'}), 500

    @app.route("/update_active_time", methods=['POST'])
    @login_required
    def update_active_time():
        """Updates the active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'seconds' not in data:
            return jsonify({'status': 'error', 'message': 'Missing seconds parameter'}), 400

        try:
            seconds = int(data['seconds'])
            if seconds <= 0:
                return jsonify({'status': 'error', 'message': 'Seconds must be positive'}), 400

            # Get today's date
            today = datetime.now().date()

            # Get or create the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            if not daily_active_time:
                daily_active_time = DailyActiveTime(
                    user_id=user_id,
                    date=today,
                    active_time=0
                )
                db.session.add(daily_active_time)

            # Update the active time
            daily_active_time.active_time += seconds

            # Update the user's last_active timestamp
            user = User.query.get(user_id)
            if user:
                user.last_active = datetime.now()

            db.session.commit()

            # Trigger feed generation for the user (but only occasionally to avoid spam)
            # Only trigger if the user has accumulated significant time (every 30 minutes)
            if daily_active_time.active_time % 1800 < seconds:  # Every 30 minutes
                try:
                    from routes.feed_utils import trigger_feed_generation_for_user
                    trigger_feed_generation_for_user(user_id)
                except Exception as e:
                    app_logger.warning(f"Failed to generate feed posts for user {user_id}: {e}")

            # Return the updated active time
            return jsonify({
                'status': 'success',
                'active_time': daily_active_time.active_time
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid seconds value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not update active time'}), 500

    @app.route("/get_active_time", methods=['GET'])
    @login_required
    def get_active_time():
        """Gets the total active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']

        try:
            # Get today's date
            today = datetime.now().date()

            # Get the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Get total active time across all days
            from sqlalchemy import func
            total_time_result = db.session.query(func.sum(DailyActiveTime.active_time)).filter(
                DailyActiveTime.user_id == user_id
            ).first()

            total_time = total_time_result[0] if total_time_result[0] else 0

            # Get the user's daily time goal
            user = User.query.get(user_id)
            daily_goal = user.daily_time_goal if user and user.daily_time_goal else 3600  # Default: 1 hour

            # Calculate progress percentage
            progress_percent = min(round((today_time / daily_goal) * 100), 100) if daily_goal > 0 else 0

            return jsonify({
                'status': 'success',
                'today_time': today_time,
                'total_time': total_time,
                'daily_goal': daily_goal,
                'progress_percent': progress_percent
            })

        except Exception as e:
            error_logger.exception(f"Error getting active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not retrieve active time'}), 500

    @app.route("/set_time_goal", methods=['POST'])
    @login_required
    def set_time_goal():
        """Sets the daily time goal for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'goal_minutes' not in data:
            return jsonify({'status': 'error', 'message': 'Missing goal_minutes parameter'}), 400

        try:
            goal_minutes = int(data['goal_minutes'])
            if goal_minutes <= 0:
                return jsonify({'status': 'error', 'message': 'Goal minutes must be positive'}), 400

            # Convert minutes to seconds
            goal_seconds = goal_minutes * 60

            # Update the user's daily time goal
            user = User.query.get(user_id)
            if not user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404

            user.daily_time_goal = goal_seconds
            db.session.commit()

            # Get today's active time for progress calculation
            today = datetime.now().date()
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Calculate progress percentage
            progress_percent = min(round((today_time / goal_seconds) * 100), 100) if goal_seconds > 0 else 0

            return jsonify({
                'status': 'success',
                'daily_goal': goal_seconds,
                'goal_minutes': goal_minutes,
                'progress_percent': progress_percent
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid goal_minutes value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error setting time goal for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not set time goal'}), 500

    @app.route('/notes')
    @login_required
    def notes():
        """Display chemistry notes chapter list"""
        try:
            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('index'))

            # Get all markdown files
            markdown_files = []
            for filename in os.listdir(notes_dir):
                if filename.endswith('.md'):
                    markdown_files.append({
                        'filename': filename,
                        'title': filename.replace('.md', '').replace('_', ' '),
                        'chapter_id': filename.replace('.md', '')
                    })

            # Sort files by filename for consistent ordering
            markdown_files.sort(key=lambda x: x['filename'])

            return render_template('notes.html', chapters=markdown_files)

        except Exception as e:
            error_logger.exception(f"Error loading chemistry notes: {e}")
            flash('Error loading chemistry notes.', 'error')
            return redirect(url_for('index'))

    @app.route('/notes/<path:chapter_id>')
    @login_required
    def view_chapter(chapter_id):
        """Display a specific chemistry notes chapter"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)

            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('notes'))

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check - ensure the file exists and is within the notes directory
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                flash('Chapter not found.', 'error')
                return redirect(url_for('notes'))

            # Read and process the markdown file
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # BETTER APPROACH: Use unique, safe placeholders that won't be modified by markdown
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    # Use UUID to ensure uniqueness and avoid conflicts
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX with safe placeholders BEFORE markdown processing
                # Process display math first ($$...$$)
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                # Process inline math ($...$)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)



                # Configure markdown with table support and TOC
                md = markdown.Markdown(extensions=[
                    'tables',  # Enable table support
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'tables': {
                        'use_align_attribute': True
                    },
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML (placeholders should be safe from modification)
                html_content = md.convert(content)

                # Extract table of contents
                toc_html = md.toc if hasattr(md, 'toc') else ''

                # Get TOC sections from database chunks instead of parsing markdown
                toc_sections = []
                try:
                    # Query database chunks for this chapter, ordered by start_line to maintain document order
                    chunks = NotesChunk.query.filter_by(chapter_id=chapter_id).order_by(NotesChunk.start_line).all()

                    for chunk in chunks:
                        toc_sections.append({
                            'id': chunk.section_id,
                            'title': chunk.title,
                            'level': chunk.level
                        })

                    app_logger.info(f"Loaded {len(toc_sections)} TOC sections from database for chapter {chapter_id}")

                except Exception as e:
                    app_logger.warning(f"Failed to load TOC sections from database for chapter {chapter_id}: {e}")
                    # Fallback to parsing TOC HTML if database query fails
                    if toc_html:
                        from html import unescape
                        from bs4 import BeautifulSoup

                        # Parse the TOC HTML to extract hierarchical structure
                        soup = BeautifulSoup(toc_html, 'html.parser')

                        def extract_toc_items(element, level=1):
                            items = []
                            for li in element.find_all('li', recursive=False):
                                link = li.find('a')
                                if link:
                                    section_id = link.get('href', '').lstrip('#')
                                    section_title = unescape(link.get_text().strip())
                                    items.append({
                                        'id': section_id,
                                        'title': section_title,
                                        'level': level
                                    })

                                    # Check for nested lists (sub-sections)
                                    nested_ul = li.find('ul')
                                    if nested_ul:
                                        items.extend(extract_toc_items(nested_ul, level + 1))
                            return items

                        # Find the main TOC list
                        main_list = soup.find('ul')
                        if main_list:
                            toc_sections = extract_toc_items(main_list)

                # Restore LaTeX from safe placeholders
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Now process LaTeX in the HTML content using a more robust approach
                def process_latex_in_html(html_text):
                    # Process display math $$...$$ first (to avoid conflicts with inline math)
                    html_text = re.sub(
                        r'\$\$([^$]+?)\$\$',
                        lambda m: f'<span class="katex-display-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text,
                        flags=re.DOTALL
                    )

                    # Process inline math $...$
                    html_text = re.sub(
                        r'\$([^$\n]+?)\$',
                        lambda m: f'<span class="katex-inline-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text
                    )

                    return html_text

                # Apply LaTeX processing to the HTML
                html_content = process_latex_in_html(html_content)

                # Post-process to fix HTML tags that should be rendered
                def fix_html_tags(html_text):
                    # Fix common HTML tags that might be escaped or not processed correctly
                    # Handle <em> tags for italics
                    html_text = re.sub(r'&lt;em&gt;(.*?)&lt;/em&gt;', r'<em>\1</em>', html_text)
                    html_text = re.sub(r'&lt;i&gt;(.*?)&lt;/i&gt;', r'<i>\1</i>', html_text)
                    html_text = re.sub(r'&lt;strong&gt;(.*?)&lt;/strong&gt;', r'<strong>\1</strong>', html_text)
                    html_text = re.sub(r'&lt;b&gt;(.*?)&lt;/b&gt;', r'<b>\1</b>', html_text)
                    html_text = re.sub(r'&lt;u&gt;(.*?)&lt;/u&gt;', r'<u>\1</u>', html_text)
                    html_text = re.sub(r'&lt;sup&gt;(.*?)&lt;/sup&gt;', r'<sup>\1</sup>', html_text)
                    html_text = re.sub(r'&lt;sub&gt;(.*?)&lt;/sub&gt;', r'<sub>\1</sub>', html_text)
                    html_text = re.sub(r'&lt;br&gt;', r'<br>', html_text)
                    html_text = re.sub(r'&lt;br/&gt;', r'<br/>', html_text)
                    html_text = re.sub(r'&lt;br /&gt;', r'<br />', html_text)

                    return html_text

                # Apply HTML tag fixes
                html_content = fix_html_tags(html_content)

                # Process image paths to use the correct route
                # Replace relative image paths with the proper route
                html_content = re.sub(
                    r'<img([^>]*?)src="images/([^"]+)"([^>]*?)>',
                    r'<img\1src="/chemistry_notes_images/\2"\3>',
                    html_content
                )

                chapter_info = {
                    'title': chapter_id.replace('_', ' '),
                    'filename': filename,
                    'html': html_content,
                    'chapter_id': chapter_id,
                    'toc_sections': toc_sections
                }

                return render_template('chapter.html', chapter=chapter_info)

            except Exception as e:
                error_logger.exception(f"Error processing markdown file {filename}: {e}")
                flash('Error processing chapter content.', 'error')
                return redirect(url_for('notes'))

        except Exception as e:
            error_logger.exception(f"Error loading chapter {chapter_id}: {e}")
            flash('Error loading chapter.', 'error')
            return redirect(url_for('notes'))

    @app.route('/notes/<path:chapter_id>/<section_id>')
    @login_required
    def view_section(chapter_id, section_id):
        """Display a specific section within a chemistry notes chapter"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)
            section_id = unquote(section_id)

            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                flash('Chemistry notes directory not found.', 'error')
                return redirect(url_for('notes'))

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check - ensure the file exists and is within the notes directory
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                flash('Chapter not found.', 'error')
                return redirect(url_for('notes'))

            # Read and process the markdown file (same as view_chapter)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Process the markdown the same way as view_chapter
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX with safe placeholders BEFORE markdown processing
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                # Configure markdown with table support and TOC
                md = markdown.Markdown(extensions=[
                    'tables',
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'tables': {
                        'use_align_attribute': True
                    },
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML
                html_content = md.convert(content)

                # Extract table of contents
                toc_html = md.toc if hasattr(md, 'toc') else ''

                # Get TOC sections from database chunks instead of parsing markdown
                toc_sections = []
                try:
                    # Query database chunks for this chapter, ordered by start_line to maintain document order
                    chunks = NotesChunk.query.filter_by(chapter_id=chapter_id).order_by(NotesChunk.start_line).all()

                    for chunk in chunks:
                        toc_sections.append({
                            'id': chunk.section_id,
                            'title': chunk.title,
                            'level': chunk.level
                        })

                    app_logger.info(f"Loaded {len(toc_sections)} TOC sections from database for chapter {chapter_id} (view_section)")

                except Exception as e:
                    app_logger.warning(f"Failed to load TOC sections from database for chapter {chapter_id} (view_section): {e}")
                    # Fallback to parsing TOC HTML if database query fails
                    if toc_html:
                        from html import unescape

                        soup = BeautifulSoup(toc_html, 'html.parser')

                        def extract_toc_items(element, level=1):
                            items = []
                            for li in element.find_all('li', recursive=False):
                                link = li.find('a')
                                if link:
                                    section_id_found = link.get('href', '').lstrip('#')
                                    section_title = unescape(link.get_text().strip())
                                    items.append({
                                        'id': section_id_found,
                                        'title': section_title,
                                        'level': level
                                    })

                                    nested_ul = li.find('ul')
                                    if nested_ul:
                                        items.extend(extract_toc_items(nested_ul, level + 1))
                            return items

                        main_list = soup.find('ul')
                        if main_list:
                            toc_sections = extract_toc_items(main_list)

                # Restore LaTeX from safe placeholders
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Process LaTeX in HTML
                def process_latex_in_html(html_text):
                    html_text = re.sub(
                        r'\$\$([^$]+?)\$\$',
                        lambda m: f'<span class="katex-display-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text,
                        flags=re.DOTALL
                    )
                    html_text = re.sub(
                        r'\$([^$\n]+?)\$',
                        lambda m: f'<span class="katex-inline-math" data-latex="{m.group(1).strip()}"></span>',
                        html_text
                    )
                    return html_text

                html_content = process_latex_in_html(html_content)

                # Fix HTML tags
                def fix_html_tags(html_text):
                    html_text = re.sub(r'&lt;em&gt;(.*?)&lt;/em&gt;', r'<em>\1</em>', html_text)
                    html_text = re.sub(r'&lt;i&gt;(.*?)&lt;/i&gt;', r'<i>\1</i>', html_text)
                    html_text = re.sub(r'&lt;strong&gt;(.*?)&lt;/strong&gt;', r'<strong>\1</strong>', html_text)
                    html_text = re.sub(r'&lt;b&gt;(.*?)&lt;/b&gt;', r'<b>\1</b>', html_text)
                    html_text = re.sub(r'&lt;u&gt;(.*?)&lt;/u&gt;', r'<u>\1</u>', html_text)
                    html_text = re.sub(r'&lt;sup&gt;(.*?)&lt;/sup&gt;', r'<sup>\1</sup>', html_text)
                    html_text = re.sub(r'&lt;sub&gt;(.*?)&lt;/sub&gt;', r'<sub>\1</sub>', html_text)
                    html_text = re.sub(r'&lt;br&gt;', r'<br>', html_text)
                    html_text = re.sub(r'&lt;br/&gt;', r'<br/>', html_text)
                    html_text = re.sub(r'&lt;br /&gt;', r'<br />', html_text)
                    return html_text

                html_content = fix_html_tags(html_content)

                # Process image paths
                html_content = re.sub(
                    r'<img([^>]*?)src="images/([^"]+)"([^>]*?)>',
                    r'<img\1src="/chemistry_notes_images/\2"\3>',
                    html_content
                )

                chapter_info = {
                    'title': chapter_id.replace('_', ' '),
                    'filename': filename,
                    'html': html_content,
                    'chapter_id': chapter_id,
                    'toc_sections': toc_sections,
                    'target_section': section_id  # Add target section for auto-scroll
                }

                return render_template('chapter.html', chapter=chapter_info)

            except Exception as e:
                error_logger.exception(f"Error processing markdown file {filename}: {e}")
                flash('Error processing chapter content.', 'error')
                return redirect(url_for('notes'))

        except Exception as e:
            error_logger.exception(f"Error loading section {section_id} in chapter {chapter_id}: {e}")
            flash('Error loading section.', 'error')
            return redirect(url_for('notes'))

    @app.route('/api/notes/sections')
    @login_required
    def get_notes_sections():
        """API endpoint to get all available sections across all chemistry notes for RAG"""
        try:
            # Get sections from database chunks instead of parsing markdown files
            all_sections = []

            try:
                # Query all chunks, ordered by chapter_id and start_line to maintain document order
                chunks = NotesChunk.query.order_by(NotesChunk.chapter_id, NotesChunk.start_line).all()

                for chunk in chunks:
                    all_sections.append({
                        'chapter_id': chunk.chapter_id,
                        'chapter_title': chunk.chapter_id.replace('_', ' '),
                        'section_id': chunk.section_id,
                        'section_title': chunk.title,
                        'level': chunk.level,
                        'url': f"/notes/{chunk.chapter_id}#{chunk.section_id}",
                        'direct_url': f"/notes/{chunk.chapter_id}/{chunk.section_id}"
                    })

                app_logger.info(f"Retrieved {len(all_sections)} sections from database chunks")

            except Exception as e:
                app_logger.warning(f"Failed to load sections from database chunks: {e}")
                # Fallback to parsing markdown files if database query fails
                notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

                if not os.path.exists(notes_dir):
                    return jsonify({'error': 'Chemistry notes directory not found and database chunks unavailable.'}), 404

                # Get all markdown files
                for filename in os.listdir(notes_dir):
                    if filename.endswith('.md'):
                        chapter_id = filename.replace('.md', '')
                        file_path = os.path.join(notes_dir, filename)

                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            # Process markdown to extract TOC
                            import uuid
                            latex_map = {}

                            def create_safe_placeholder(match):
                                placeholder_id = str(uuid.uuid4()).replace('-', '')
                                safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                                latex_map[safe_placeholder] = match.group(0)
                                return safe_placeholder

                            # Protect LaTeX
                            content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                            content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                            # Configure markdown with TOC
                            md = markdown.Markdown(extensions=[
                                'tables',
                                'fenced_code',
                                'codehilite',
                                'toc'
                            ], extension_configs={
                                'toc': {
                                    'permalink': True,
                                    'permalink_class': 'section-link',
                                    'permalink_title': 'Link to this section',
                                    'baselevel': 1,
                                    'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                                }
                            }, output_format='html')

                            # Convert markdown to HTML
                            html_content = md.convert(content)

                            # Extract table of contents
                            toc_html = md.toc if hasattr(md, 'toc') else ''

                            # Parse TOC to extract section data
                            if toc_html:
                                from html import unescape

                                soup = BeautifulSoup(toc_html, 'html.parser')

                                def extract_toc_items(element, level=1):
                                    items = []
                                    for li in element.find_all('li', recursive=False):
                                        link = li.find('a')
                                        if link:
                                            section_id_found = link.get('href', '').lstrip('#')
                                            section_title = unescape(link.get_text().strip())
                                            items.append({
                                                'chapter_id': chapter_id,
                                                'chapter_title': chapter_id.replace('_', ' '),
                                                'section_id': section_id_found,
                                                'section_title': section_title,
                                                'level': level,
                                                'url': f"/notes/{chapter_id}#{section_id_found}",
                                                'direct_url': f"/notes/{chapter_id}/{section_id_found}"
                                            })

                                            nested_ul = li.find('ul')
                                            if nested_ul:
                                                items.extend(extract_toc_items(nested_ul, level + 1))
                                    return items

                                main_list = soup.find('ul')
                                if main_list:
                                    chapter_sections = extract_toc_items(main_list)
                                    all_sections.extend(chapter_sections)

                        except Exception as e:
                            error_logger.exception(f"Error processing {filename}: {e}")
                            continue

            return jsonify({
                'sections': all_sections,
                'total_sections': len(all_sections),
                'message': 'Successfully retrieved all chemistry notes sections'
            })

        except Exception as e:
            error_logger.exception(f"Error getting notes sections: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/api/notes/<path:chapter_id>/<section_id>')
    @login_required
    def get_section_content(chapter_id, section_id):
        """API endpoint to get specific section content for RAG"""
        try:
            # URL decode the chapter_id to handle spaces and special characters
            from urllib.parse import unquote
            chapter_id = unquote(chapter_id)
            section_id = unquote(section_id)

            # First try to get content from database chunks
            try:
                chunk = NotesChunk.query.filter_by(
                    chapter_id=chapter_id,
                    section_id=section_id
                ).first()

                if chunk:
                    app_logger.info(f"Retrieved section content from database chunk for {chapter_id}/{section_id}")
                    return jsonify({
                        'chapter_id': chunk.chapter_id,
                        'chapter_title': chunk.chapter_id.replace('_', ' '),
                        'section_id': chunk.section_id,
                        'section_title': chunk.title,
                        'content_html': chunk.content,  # Chunk content is already processed
                        'content_text': BeautifulSoup(chunk.content, 'html.parser').get_text(),
                        'url': f"/notes/{chunk.chapter_id}#{chunk.section_id}",
                        'direct_url': f"/notes/{chunk.chapter_id}/{chunk.section_id}",
                        'level': chunk.level,
                        'word_count': chunk.word_count,
                        'char_count': chunk.char_count
                    })
                else:
                    app_logger.warning(f"Section {section_id} not found in database chunks for chapter {chapter_id}")

            except Exception as e:
                app_logger.warning(f"Failed to retrieve section from database chunks for {chapter_id}/{section_id}: {e}")

            # Fallback to parsing markdown file if database chunk not found
            # Get the chemistry_notes_markdown directory path
            notes_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown')

            if not os.path.exists(notes_dir):
                return jsonify({'error': 'Chemistry notes directory not found and database chunk unavailable.'}), 404

            # Construct the file path
            filename = f"{chapter_id}.md"
            file_path = os.path.join(notes_dir, filename)

            # Security check
            if not os.path.exists(file_path) or not os.path.abspath(file_path).startswith(os.path.abspath(notes_dir)):
                return jsonify({'error': 'Chapter not found.'}), 404

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Process markdown to extract the specific section
                import uuid
                latex_map = {}

                def create_safe_placeholder(match):
                    placeholder_id = str(uuid.uuid4()).replace('-', '')
                    safe_placeholder = f"LATEXSAFE{placeholder_id}LATEXSAFE"
                    latex_map[safe_placeholder] = match.group(0)
                    return safe_placeholder

                # Protect LaTeX
                content = re.sub(r'\$\$([^$]+?)\$\$', create_safe_placeholder, content, flags=re.DOTALL)
                content = re.sub(r'\$([^$\n]+?)\$', create_safe_placeholder, content)

                # Configure markdown
                md = markdown.Markdown(extensions=[
                    'tables',
                    'fenced_code',
                    'codehilite',
                    'toc'
                ], extension_configs={
                    'toc': {
                        'permalink': True,
                        'permalink_class': 'section-link',
                        'permalink_title': 'Link to this section',
                        'baselevel': 1,
                        'slugify': lambda value, separator: re.sub(r'[-\s]+', separator, re.sub(r'[^\w\s-]', '', value.replace('°', 'circ')).strip().lower())
                    }
                }, output_format='html')

                # Convert markdown to HTML
                html_content = md.convert(content)

                # Restore LaTeX
                for placeholder, original_latex in latex_map.items():
                    html_content = html_content.replace(placeholder, original_latex)

                # Extract the specific section content
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')

                # Find the target section
                target_element = soup.find(id=section_id)
                if not target_element:
                    return jsonify({'error': f'Section {section_id} not found.'}), 404

                # Extract content from this section until the next heading of same or higher level
                section_content = []
                current_element = target_element
                target_level = int(target_element.name[1]) if target_element.name.startswith('h') else 1

                # Add the heading itself
                section_content.append(str(current_element))

                # Get all following siblings until next heading of same or higher level
                for sibling in current_element.next_siblings:
                    if hasattr(sibling, 'name') and sibling.name and sibling.name.startswith('h'):
                        sibling_level = int(sibling.name[1])
                        if sibling_level <= target_level:
                            break
                    section_content.append(str(sibling))

                section_html = ''.join(section_content)

                return jsonify({
                    'chapter_id': chapter_id,
                    'chapter_title': chapter_id.replace('_', ' '),
                    'section_id': section_id,
                    'section_title': target_element.get_text().strip(),
                    'content_html': section_html,
                    'content_text': BeautifulSoup(section_html, 'html.parser').get_text(),
                    'url': f"/notes/{chapter_id}#{section_id}",
                    'direct_url': f"/notes/{chapter_id}/{section_id}"
                })

            except Exception as e:
                error_logger.exception(f"Error processing section {section_id} in {filename}: {e}")
                return jsonify({'error': 'Error processing section content.'}), 500

        except Exception as e:
            error_logger.exception(f"Error getting section content for {chapter_id}/{section_id}: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/api/notes/search', methods=['POST'])
    @login_required
    def search_notes():
        """
        Search chemistry notes using RAG system.

        Expects JSON payload:
        {
            "query": "search query",
            "top_k": 5,  // optional, default 5
            "min_score": 0.3  // optional, default 0.3
        }
        """
        try:
            data = request.get_json()
            if not data or 'query' not in data:
                return jsonify({'error': 'Query is required'}), 400

            query = data['query'].strip()
            if not query:
                return jsonify({'error': 'Query cannot be empty'}), 400

            top_k = data.get('top_k', 5)
            min_score = data.get('min_score', 0.3)

            # Validate parameters
            if not isinstance(top_k, int) or top_k < 1 or top_k > 20:
                return jsonify({'error': 'top_k must be an integer between 1 and 20'}), 400

            if not isinstance(min_score, (int, float)) or min_score < 0 or min_score > 1:
                return jsonify({'error': 'min_score must be a number between 0 and 1'}), 400

            # Check if RAG dependencies are available
            try:
                from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
                if not DEPENDENCIES_AVAILABLE:
                    return jsonify({'error': 'RAG system dependencies not available'}), 503
            except ImportError:
                return jsonify({'error': 'RAG system not available'}), 503

            # Initialize and use RAG system
            rag_system = NotesRAGSystem()

            # Search for similar chunks
            results = rag_system.search_similar_chunks(query, top_k=top_k, min_score=min_score)

            # Format results for response
            formatted_results = []
            for result in results:
                chunk = result['chunk']
                formatted_result = {
                    'id': chunk['id'],
                    'title': chunk['title'],
                    'content': chunk['content'][:500] + '...' if len(chunk['content']) > 500 else chunk['content'],
                    'full_content': chunk['content'],
                    'filename': chunk['filename'],
                    'chapter_id': chunk['chapter_id'],
                    'section_id': chunk['section_id'],
                    'level': chunk['level'],
                    'parent_sections': chunk['parent_sections'],
                    'similarity_score': result['similarity_score'],
                    'relevance_type': result['relevance_type'],
                    'word_count': chunk['word_count'],
                    'char_count': chunk['char_count']
                }
                formatted_results.append(formatted_result)

            return jsonify({
                'status': 'success',
                'query': query,
                'results': formatted_results,
                'total_results': len(formatted_results),
                'parameters': {
                    'top_k': top_k,
                    'min_score': min_score
                }
            })

        except Exception as e:
            error_logger.exception(f"Error in notes search: {e}")
            return jsonify({'error': 'Internal server error during search'}), 500

    @app.route('/api/notes/chunk/<int:chunk_id>/context')
    @login_required
    def get_chunk_context(chunk_id):
        """
        Get contextual information for a specific chunk including parent and sibling sections.
        """
        try:
            # Check if RAG dependencies are available
            try:
                from notes_rag_system import NotesRAGSystem, DEPENDENCIES_AVAILABLE
                if not DEPENDENCIES_AVAILABLE:
                    return jsonify({'error': 'RAG system dependencies not available'}), 503
            except ImportError:
                return jsonify({'error': 'RAG system not available'}), 503

            # Initialize RAG system
            rag_system = NotesRAGSystem()

            # Get chunk context
            context = rag_system.get_chunk_context(chunk_id, include_siblings=True)

            if not context:
                return jsonify({'error': 'Chunk not found'}), 404

            return jsonify({
                'status': 'success',
                'context': context
            })

        except Exception as e:
            error_logger.exception(f"Error getting chunk context: {e}")
            return jsonify({'error': 'Internal server error'}), 500

    @app.route('/chemistry_notes_images/<path:filename>')
    def serve_chemistry_notes_image(filename):
        """Serve images from chemistry_notes_markdown/images directory"""
        try:
            # Basic security check
            if '..' in filename or filename.startswith('/'):
                return "Invalid path", 400

            images_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown', 'images')

            # Ensure the resolved path is still within the images directory
            safe_file_path = os.path.abspath(os.path.join(images_dir, filename))

            if not safe_file_path.startswith(images_dir) or not os.path.exists(safe_file_path):
                return "File not found", 404

            from flask import send_from_directory
            return send_from_directory(images_dir, filename)

        except Exception as e:
            error_logger.exception(f"Error serving chemistry notes image {filename}: {e}")
            return "Error serving image", 500

    @app.route('/api/get-random-tour-dojo-question', methods=['GET'])
    @login_required
    def get_random_tour_dojo_question():
        """Get a random DOJO question from core topics for the tour"""
        try:
            from models import Topic, Question, Subject
            import random

            # Core topics for the tour
            core_topics = ['energetics', 'chemical-bonding-1', 'periodic-table']

            # Get chemistry subject
            chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
            if not chemistry_subject:
                return jsonify({'success': False, 'message': 'Chemistry subject not found'})

            # Collect all DOJO questions from core topics
            all_questions = []
            for topic_name in core_topics:
                topic = Topic.query.filter_by(name=topic_name, subject_id=chemistry_subject.id).first()
                if topic:
                    questions = Question.query.filter_by(topic_id=topic.id, is_dojo=True).all()
                    for q in questions:
                        all_questions.append({
                            'id': q.id,
                            'title': q.title,
                            'topic_name': topic.name,
                            'topic_display_name': topic.name.replace('-', ' ').title()
                        })

            if not all_questions:
                return jsonify({'success': False, 'message': 'No DOJO questions found in core topics'})

            # Select a random question
            selected_question = random.choice(all_questions)

            return jsonify({
                'success': True,
                'question': selected_question
            })

        except Exception as e:
            error_logger.exception(f"Error getting random tour DOJO question: {str(e)}")
            return jsonify({'success': False, 'message': 'Failed to get random question'})
