#!/usr/bin/env python3
"""
Test URL encoding/decoding for the GET request approach
"""

import urllib.parse

def test_url_encoding():
    """Test various answer types with URL encoding"""
    
    test_answers = [
        "Simple answer",
        "Answer with spaces and punctuation!",
        "Answer with <HTML> tags & symbols",
        "Math: x^2 + y^2 = z^2",
        "LaTeX: $\\frac{1}{2}$ and $x^{2}$",
        "Special chars: @#$%^&*()",
        "Unicode: café résumé naïve",
        "Newlines:\nLine 1\nLine 2",
        "Quotes: 'single' and \"double\"",
        ""  # Empty string
    ]
    
    print("Testing URL encoding/decoding for GET requests:")
    print("=" * 60)
    
    for i, answer in enumerate(test_answers, 1):
        print(f"\nTest {i}: Original answer:")
        print(f"  '{answer}'")
        
        # Encode (frontend)
        encoded = urllib.parse.quote(answer)
        print(f"  Encoded: '{encoded}'")
        
        # Decode (backend)
        decoded = urllib.parse.unquote(encoded)
        print(f"  Decoded: '{decoded}'")
        
        # Check if round-trip works
        if answer == decoded:
            print("  ✅ Round-trip successful")
        else:
            print("  ❌ Round-trip failed!")
            print(f"     Expected: '{answer}'")
            print(f"     Got:      '{decoded}'")
    
    print("\n" + "=" * 60)
    print("URL encoding test complete!")

def test_flask_route_format():
    """Test the Flask route format"""
    print("\nTesting Flask route format:")
    print("=" * 40)
    
    question_id = 123
    part_id = 456
    answer = "This is a test answer with spaces!"
    
    encoded_answer = urllib.parse.quote(answer)
    route = f"/get_highlighted_answer/{question_id}/{part_id}/{encoded_answer}"
    
    print(f"Question ID: {question_id}")
    print(f"Part ID: {part_id}")
    print(f"Original answer: '{answer}'")
    print(f"Encoded answer: '{encoded_answer}'")
    print(f"Full route: {route}")
    
    # Simulate Flask path parameter extraction
    path_parts = route.split('/')
    extracted_answer = urllib.parse.unquote(path_parts[-1])
    
    print(f"Extracted answer: '{extracted_answer}'")
    print(f"Match: {'✅' if answer == extracted_answer else '❌'}")

if __name__ == "__main__":
    test_url_encoding()
    test_flask_route_format()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("✅ GET requests are simpler than POST")
    print("✅ No form data or request body needed")
    print("✅ No CSRF token issues")
    print("✅ URL encoding handles special characters")
    print("✅ Flask path parameters work well")
    print("\nThis approach should resolve the 'undefined' issue!")
